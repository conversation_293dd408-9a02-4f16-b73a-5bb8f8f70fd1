import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/flower.dart';
import '../../models/tag_info.dart';
import '../../widgets/ftm_box.dart';
import 'widgets/add_flower_tag_selector.dart';
import 'widgets/alarm_list_widget.dart';
import 'widgets/arrival_time_widget.dart';
import 'widgets/avatar_changeable.dart';

final selectItem1 = Iterable.generate(366, (i) => '$i').toList();

class AddFlowerPage extends ConsumerStatefulWidget {
  const AddFlowerPage({this.flower, super.key});

  final Flower? flower;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AddFlowerPageState();
}

class _AddFlowerPageState extends ConsumerState<AddFlowerPage> {
  final GlobalKey _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late AddFlowerController controller;
  final List<TagInfo> selectedTags = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Text(widget.flower == null ? "record_new_flower".tr() : "editing".tr()),
          elevation: 0,
          actions: [TextButton(onPressed: onSave, child: const Text("save").tr())],
          leading: TextButton(onPressed: onCancel, child: const Text("cancel").tr()),
          leadingWidth: 68,
        ),
        body: WillPopScope(
            onWillPop: () async { return false; },
            child: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
                child: Column(
                  children: [
                    buildBaseInfoWidget(),
                    buildAlarmInfoWidget(),
                    buildOtherOption()
                  ],
                ),
              )
            )
        )
    );
  }

  Widget buildBaseInfoWidget() {
    return FTMBox(
      circular: 10,
      child: Form(
        key: _formKey,
        child: Container(
            padding: const EdgeInsets.all(10),
            child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  AvatarChangeableWidget(
                      height: 65,
                      width: 65,
                      avatar: controller.avatar,
                      onChange: (imageData) {
                        controller.setNewAvatar(imageData);
                  }),
                  const SizedBox(width: 10),
                  Flexible(child: TextFormField(
                      controller: _nameController,
                      validator: nameValidator,
                      decoration: InputDecoration(hintText: "flower_name_edit_prompt".tr())
                  )),
                ]
            )
        ),
      ),
    );
  }

  Widget buildAlarmInfoWidget() {
    return FTMBox(
        circular: 10,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: ref.watch(loadFlowerNurtureTypeCycleProvider(controller)).when(
              data: (_) => AddFlowerAlarmListWidget(controller: controller),
              loading: () => AddFlowerAlarmListWidget(controller: controller),
              error: (e, s) => ErrorReporter(error: e, stackTrace: s)
          ),
        )
    );
  }



  Widget buildOtherOption() {
    return FTMBox(
        circular: 10,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Column(
            children: [
              AddFlowerTagSelector(selectedTags: selectedTags),
              ArrivalTimeWidget(controller: controller)
            ],
          ),
        )
    );
  }

  String? nameValidator(v) {
    if (v!.trim().isEmpty) {
      return "add_flower.flower_name_cannot_empty".tr();
    }
    return null;
  }


  void onSave() async {
    if (!(_formKey.currentState as FormState).validate()) {
      return;
    }

    controller.selectedTags = selectedTags;
    controller.name = _nameController.text;

    if (!controller.isChange()) {
      Navigator.pop(context, null);
    }

    controller.save().then((flower) => Navigator.pop(context, flower));
  }

  void onCancel() async {
    controller.selectedTags = selectedTags;
    controller.name = _nameController.text;

    if (controller.isChange()) {
      final isExit = await showAlertDialog('alert'.tr(), 'unsave_alert'.tr(), context);
      if (isExit == null || !isExit) {
        return;
      }
    }

    if (context.mounted) {
      Navigator.pop(context, null);
    }
  }

  @override
  void initState() {
    super.initState();

    controller = ref.read(getAddFlowerControllerProvider(widget.flower));
    _nameController = TextEditingController(text: controller.name);
    selectedTags.addAll(controller.selectedTags);
  }
}
