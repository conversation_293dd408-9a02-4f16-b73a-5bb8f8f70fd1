import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flower_timemachine/models/flower_monthly_nurture_cycle.dart';
import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';

class MonthlyNurtureCycleRepository {
  // 查询某个植物的所有养护类型的月份周期
  Future<FlowerMonthlyCycles> getFlowerMonthlyCycles(int flowerId) async {
    final records = await FlowerMonthlyNurtureCycle.getByFlower(flowerId);

    final Map<int, MonthlyCycleData> typesCycles = {};

    // 按养护类型分组
    final Map<int, List<FlowerMonthlyNurtureCycle>> groupedByType = {};
    for (final record in records) {
      groupedByType.putIfAbsent(record.typeId, () => []).add(record);
    }

    // 为每个养护类型构建月份周期数据
    for (final entry in groupedByType.entries) {
      final typeId = entry.key;
      final typeRecords = entry.value;

      // 初始化12个月的数据为未设置(-1)
      final List<int> cycles = List.filled(12, -1);

      // 填充实际数据
      for (final record in typeRecords) {
        if (record.month >= 1 && record.month <= 12) {
          cycles[record.month - 1] = record.cycle;
        }
      }

      typesCycles[typeId] = MonthlyCycleData(cycles: cycles);
    }

    return FlowerMonthlyCycles(typesCycles: typesCycles);
  }

  // 查询某个植物的某种养护类型的月份周期
  Future<MonthlyCycleData> getFlowerTypeMonthlyData(int flowerId, int typeId) async {
    final records = await FlowerMonthlyNurtureCycle.getByFlowerAndType(flowerId, typeId);

    // 初始化12个月的数据为未设置(-1)
    final List<int> cycles = List.filled(12, -1);

    // 填充实际数据
    for (final record in records) {
      if (record.month >= 1 && record.month <= 12) {
        cycles[record.month - 1] = record.cycle;
      }
    }

    return MonthlyCycleData(cycles: cycles);
  }

  // 保存某个植物的所有养护类型的月份周期
  Future<void> saveFlowerMonthlyCycles(int flowerId, FlowerMonthlyCycles flowerCycles) async {
    // 先删除现有数据
    await FlowerMonthlyNurtureCycle.deleteByFlower(flowerId);

    // 保存新数据
    for (final entry in flowerCycles.typesCycles.entries) {
      final typeId = entry.key;
      final cycleData = entry.value;
      await FlowerMonthlyNurtureCycle.createBatch(flowerId, typeId, cycleData.cycles);
    }
  }

  // 保存某个植物的某种养护类型的月份周期
  Future<void> saveFlowerTypeMonthlyData(int flowerId, int typeId, MonthlyCycleData cycleData) async {
    await FlowerMonthlyNurtureCycle.updateBatch(flowerId, typeId, cycleData.cycles);
  }

  // 删除某个植物的所有月份周期数据
  Future<void> deleteFlowerMonthlyCycles(int flowerId) async {
    await FlowerMonthlyNurtureCycle.deleteByFlower(flowerId);
  }

  // 删除某个养护类型的所有月份周期数据
  Future<void> deleteNurtureTypeMonthlyCycles(int nurtureTypeId) async {
    await FlowerMonthlyNurtureCycle.deleteByNurtureType(nurtureTypeId);
  }

  // 从旧的养护周期数据迁移到新的月份周期数据
  Future<void> migrateFromLegacyCycles(int flowerId, Map<int, int> legacyCycles) async {
    final flowerCycles = FlowerMonthlyCycles.fromLegacyCycles(legacyCycles);
    await saveFlowerMonthlyCycles(flowerId, flowerCycles);
  }
}

// Riverpod Provider
final monthlyNurtureCycleRepositoryProvider = Provider<MonthlyNurtureCycleRepository>((ref) {
  return MonthlyNurtureCycleRepository();
});
