import 'package:flower_timemachine/types/monthly_cycle_data.dart';
import 'package:flower_timemachine/models/nurture_types.dart';

class FlowerMonthlyCycles {
  Map<int, MonthlyCycleData> typesCycles; // 养护类型ID -> 月份周期数据

  FlowerMonthlyCycles({required this.typesCycles});

  // 获取某个养护类型的月份周期数据
  MonthlyCycleData? getCycleDataForType(int typeId) {
    return typesCycles[typeId];
  }

  // 设置某个养护类型的月份周期数据
  FlowerMonthlyCycles setCycleDataForType(int typeId, MonthlyCycleData cycleData) {
    final newTypesCycles = Map<int, MonthlyCycleData>.from(typesCycles);
    newTypesCycles[typeId] = cycleData;
    return copyWith(typesCycles: newTypesCycles);
  }

  // 获取某个养护类型某月的周期
  int getCycleForTypeAndMonth(int typeId, int month) {
    final cycleData = typesCycles[typeId];
    if (cycleData == null) {
      return -1; // 未设置
    }
    return cycleData.getCycleForMonth(month);
  }

  // 设置某个养护类型某月的周期
  FlowerMonthlyCycles setCycleForTypeAndMonth(int typeId, int month, int cycle) {
    final currentCycleData = typesCycles[typeId] ?? MonthlyCycleData.createDefault();
    final newCycleData = currentCycleData.setCycleForMonth(month, cycle);
    return setCycleDataForType(typeId, newCycleData);
  }

  // 获取某个养护类型某月的实际周期（处理继承逻辑）
  int getEffectiveCycleForTypeAndMonth(int typeId, int month, NurtureType nurtureType) {
    final cycleData = typesCycles[typeId];
    if (cycleData == null) {
      return nurtureType.defaultCycle;
    }
    return cycleData.getEffectiveCycleForMonth(month, nurtureType.defaultCycle);
  }

  // 移除某个养护类型的数据
  FlowerMonthlyCycles removeType(int typeId) {
    final newTypesCycles = Map<int, MonthlyCycleData>.from(typesCycles);
    newTypesCycles.remove(typeId);
    return copyWith(typesCycles: newTypesCycles);
  }

  // 创建空的花卉月份周期数据
  static FlowerMonthlyCycles createEmpty() {
    return const FlowerMonthlyCycles(typesCycles: {});
  }

  // 从旧的养护周期数据创建（迁移用）
  static FlowerMonthlyCycles fromLegacyCycles(Map<int, int> legacyCycles) {
    final Map<int, MonthlyCycleData> typesCycles = {};

    for (final entry in legacyCycles.entries) {
      final typeId = entry.key;
      final cycle = entry.value;

      // 旧数据中 0 表示未设置，转换为新数据中的 -1
      final newCycle = cycle == 0 ? -1 : cycle;
      typesCycles[typeId] = MonthlyCycleData.fromSingleCycle(newCycle);
    }

    return FlowerMonthlyCycles(typesCycles: typesCycles);
  }
}
