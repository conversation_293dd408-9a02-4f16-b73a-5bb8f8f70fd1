// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'monthly_cycle_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$MonthlyCycleData {
  List<int> get cycles => throw _privateConstructorUsedError;

  /// Create a copy of MonthlyCycleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MonthlyCycleDataCopyWith<MonthlyCycleData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MonthlyCycleDataCopyWith<$Res> {
  factory $MonthlyCycleDataCopyWith(
          MonthlyCycleData value, $Res Function(MonthlyCycleData) then) =
      _$MonthlyCycleDataCopyWithImpl<$Res, MonthlyCycleData>;
  @useResult
  $Res call({List<int> cycles});
}

/// @nodoc
class _$MonthlyCycleDataCopyWithImpl<$Res, $Val extends MonthlyCycleData>
    implements $MonthlyCycleDataCopyWith<$Res> {
  _$MonthlyCycleDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MonthlyCycleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cycles = null,
  }) {
    return _then(_value.copyWith(
      cycles: null == cycles
          ? _value.cycles
          : cycles // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MonthlyCycleDataImplCopyWith<$Res>
    implements $MonthlyCycleDataCopyWith<$Res> {
  factory _$$MonthlyCycleDataImplCopyWith(_$MonthlyCycleDataImpl value,
          $Res Function(_$MonthlyCycleDataImpl) then) =
      __$$MonthlyCycleDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<int> cycles});
}

/// @nodoc
class __$$MonthlyCycleDataImplCopyWithImpl<$Res>
    extends _$MonthlyCycleDataCopyWithImpl<$Res, _$MonthlyCycleDataImpl>
    implements _$$MonthlyCycleDataImplCopyWith<$Res> {
  __$$MonthlyCycleDataImplCopyWithImpl(_$MonthlyCycleDataImpl _value,
      $Res Function(_$MonthlyCycleDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of MonthlyCycleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cycles = null,
  }) {
    return _then(_$MonthlyCycleDataImpl(
      cycles: null == cycles
          ? _value._cycles
          : cycles // ignore: cast_nullable_to_non_nullable
              as List<int>,
    ));
  }
}

/// @nodoc

class _$MonthlyCycleDataImpl extends _MonthlyCycleData {
  const _$MonthlyCycleDataImpl({required final List<int> cycles})
      : _cycles = cycles,
        super._();

  final List<int> _cycles;
  @override
  List<int> get cycles {
    if (_cycles is EqualUnmodifiableListView) return _cycles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_cycles);
  }

  @override
  String toString() {
    return 'MonthlyCycleData(cycles: $cycles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonthlyCycleDataImpl &&
            const DeepCollectionEquality().equals(other._cycles, _cycles));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_cycles));

  /// Create a copy of MonthlyCycleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MonthlyCycleDataImplCopyWith<_$MonthlyCycleDataImpl> get copyWith =>
      __$$MonthlyCycleDataImplCopyWithImpl<_$MonthlyCycleDataImpl>(
          this, _$identity);
}

abstract class _MonthlyCycleData extends MonthlyCycleData {
  const factory _MonthlyCycleData({required final List<int> cycles}) =
      _$MonthlyCycleDataImpl;
  const _MonthlyCycleData._() : super._();

  @override
  List<int> get cycles;

  /// Create a copy of MonthlyCycleData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MonthlyCycleDataImplCopyWith<_$MonthlyCycleDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
