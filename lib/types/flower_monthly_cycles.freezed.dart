// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flower_monthly_cycles.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FlowerMonthlyCycles {
  Map<int, MonthlyCycleData> get typesCycles =>
      throw _privateConstructorUsedError;

  /// Create a copy of FlowerMonthlyCycles
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FlowerMonthlyCyclesCopyWith<FlowerMonthlyCycles> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlowerMonthlyCyclesCopyWith<$Res> {
  factory $FlowerMonthlyCyclesCopyWith(
          FlowerMonthlyCycles value, $Res Function(FlowerMonthlyCycles) then) =
      _$FlowerMonthlyCyclesCopyWithImpl<$Res, FlowerMonthlyCycles>;
  @useResult
  $Res call({Map<int, MonthlyCycleData> typesCycles});
}

/// @nodoc
class _$FlowerMonthlyCyclesCopyWithImpl<$Res, $Val extends FlowerMonthlyCycles>
    implements $FlowerMonthlyCyclesCopyWith<$Res> {
  _$FlowerMonthlyCyclesCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FlowerMonthlyCycles
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? typesCycles = null,
  }) {
    return _then(_value.copyWith(
      typesCycles: null == typesCycles
          ? _value.typesCycles
          : typesCycles // ignore: cast_nullable_to_non_nullable
              as Map<int, MonthlyCycleData>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FlowerMonthlyCyclesImplCopyWith<$Res>
    implements $FlowerMonthlyCyclesCopyWith<$Res> {
  factory _$$FlowerMonthlyCyclesImplCopyWith(_$FlowerMonthlyCyclesImpl value,
          $Res Function(_$FlowerMonthlyCyclesImpl) then) =
      __$$FlowerMonthlyCyclesImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Map<int, MonthlyCycleData> typesCycles});
}

/// @nodoc
class __$$FlowerMonthlyCyclesImplCopyWithImpl<$Res>
    extends _$FlowerMonthlyCyclesCopyWithImpl<$Res, _$FlowerMonthlyCyclesImpl>
    implements _$$FlowerMonthlyCyclesImplCopyWith<$Res> {
  __$$FlowerMonthlyCyclesImplCopyWithImpl(_$FlowerMonthlyCyclesImpl _value,
      $Res Function(_$FlowerMonthlyCyclesImpl) _then)
      : super(_value, _then);

  /// Create a copy of FlowerMonthlyCycles
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? typesCycles = null,
  }) {
    return _then(_$FlowerMonthlyCyclesImpl(
      typesCycles: null == typesCycles
          ? _value._typesCycles
          : typesCycles // ignore: cast_nullable_to_non_nullable
              as Map<int, MonthlyCycleData>,
    ));
  }
}

/// @nodoc

class _$FlowerMonthlyCyclesImpl extends _FlowerMonthlyCycles {
  const _$FlowerMonthlyCyclesImpl(
      {required final Map<int, MonthlyCycleData> typesCycles})
      : _typesCycles = typesCycles,
        super._();

  final Map<int, MonthlyCycleData> _typesCycles;
  @override
  Map<int, MonthlyCycleData> get typesCycles {
    if (_typesCycles is EqualUnmodifiableMapView) return _typesCycles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_typesCycles);
  }

  @override
  String toString() {
    return 'FlowerMonthlyCycles(typesCycles: $typesCycles)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FlowerMonthlyCyclesImpl &&
            const DeepCollectionEquality()
                .equals(other._typesCycles, _typesCycles));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_typesCycles));

  /// Create a copy of FlowerMonthlyCycles
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FlowerMonthlyCyclesImplCopyWith<_$FlowerMonthlyCyclesImpl> get copyWith =>
      __$$FlowerMonthlyCyclesImplCopyWithImpl<_$FlowerMonthlyCyclesImpl>(
          this, _$identity);
}

abstract class _FlowerMonthlyCycles extends FlowerMonthlyCycles {
  const factory _FlowerMonthlyCycles(
          {required final Map<int, MonthlyCycleData> typesCycles}) =
      _$FlowerMonthlyCyclesImpl;
  const _FlowerMonthlyCycles._() : super._();

  @override
  Map<int, MonthlyCycleData> get typesCycles;

  /// Create a copy of FlowerMonthlyCycles
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FlowerMonthlyCyclesImplCopyWith<_$FlowerMonthlyCyclesImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
