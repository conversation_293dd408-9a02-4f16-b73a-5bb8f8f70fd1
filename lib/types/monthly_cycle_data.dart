import 'package:freezed_annotation/freezed_annotation.dart';

part 'monthly_cycle_data.freezed.dart';

@freezed
class MonthlyCycleData with _$MonthlyCycleData {
  const factory MonthlyCycleData({
    required List<int> cycles, // 12个月的周期数据，-1=未设置, 0=继承, >0=天数
  }) = _MonthlyCycleData;

  const MonthlyCycleData._();

  // 获取某月的周期
  int getCycleForMonth(int month) {
    if (month < 1 || month > 12) {
      throw ArgumentError('月份必须在1-12之间');
    }
    return cycles[month - 1];
  }

  // 设置某月的周期
  MonthlyCycleData setCycleForMonth(int month, int cycle) {
    if (month < 1 || month > 12) {
      throw ArgumentError('月份必须在1-12之间');
    }
    final newCycles = List<int>.from(cycles);
    newCycles[month - 1] = cycle;
    return copyWith(cycles: newCycles);
  }

  // 获取某月的实际周期（处理继承逻辑）
  int getEffectiveCycleForMonth(int month, int defaultCycle) {
    if (month < 1 || month > 12) {
      throw ArgumentError('月份必须在1-12之间');
    }
    
    final cycle = cycles[month - 1];
    
    // 如果是具体天数，直接返回
    if (cycle > 0) {
      return cycle;
    }
    
    // 如果是未设置(-1)，返回默认周期
    if (cycle == -1) {
      return defaultCycle;
    }
    
    // 如果是继承(0)，向前查找最近的非继承值
    for (int i = month - 2; i >= 0; i--) {
      final prevCycle = cycles[i];
      if (prevCycle > 0) {
        return prevCycle;
      }
      if (prevCycle == -1) {
        return defaultCycle;
      }
    }
    
    // 如果前面都是继承，返回默认周期
    return defaultCycle;
  }

  // 创建默认的月份周期数据（所有月份都未设置）
  static MonthlyCycleData createDefault() {
    return const MonthlyCycleData(cycles: [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]);
  }

  // 从单个周期值创建（所有月份使用相同值）
  static MonthlyCycleData fromSingleCycle(int cycle) {
    return MonthlyCycleData(cycles: List.filled(12, cycle));
  }
}
